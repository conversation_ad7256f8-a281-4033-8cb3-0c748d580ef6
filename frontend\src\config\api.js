// Centralized API configuration
const API_BASE_URL = '/api';

// API endpoints
export const API_ENDPOINTS = {
  // File upload endpoints
  UPLOAD_PROPOSAL: `${API_BASE_URL}/upload/proposal`,
  UPLOAD_FINANCIALS: `${API_BASE_URL}/upload/financials`,
  
  // Quote generation endpoints
  GENERATE_QUOTE: `${API_BASE_URL}/generate_quote`,
  DOWNLOAD_QUOTE: `${API_BASE_URL}/download_quote`,
  
  // Data management endpoints
  CLEAR_PROPOSAL: `${API_BASE_URL}/clear_proposal`,
  EDIT_PROPOSAL_FIELD: `${API_BASE_URL}/edit_proposal_field`,
  FORMAT_NUMBER: `${API_BASE_URL}/format_number`,
  
  // Text extraction endpoints (if needed)
  GET_PROPOSAL_TEXT: (filename) => `${API_BASE_URL}/get_text/proposal/${filename}`,
  GET_FINANCIALS_TEXT: (filename) => `${API_BASE_URL}/get_text/financials/${filename}`,
  CLEAR_TEXT: `${API_BASE_URL}/clear_text`
};

// Common fetch configuration
export const fetchConfig = {
  headers: {
    'Content-Type': 'application/json',
  },
  credentials: 'include'
};

// Helper function for API calls
export const apiCall = async (endpoint, options = {}) => {
  const config = {
    ...fetchConfig,
    ...options,
    headers: {
      ...fetchConfig.headers,
      ...options.headers
    }
  };

  try {
    const response = await fetch(endpoint, config);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
};

// Specific API functions
export const uploadFiles = async (files, type = 'proposal') => {
  const formData = new FormData();
  files.forEach((file) => {
    formData.append('files', file);
  });

  const endpoint = type.toLowerCase() === 'financial' 
    ? API_ENDPOINTS.UPLOAD_FINANCIALS 
    : API_ENDPOINTS.UPLOAD_PROPOSAL;

  return fetch(endpoint, {
    method: 'POST',
    body: formData,
    credentials: 'include'
  }).then(response => {
    if (!response.ok) {
      return response.json().then(data => {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      });
    }
    return response.json();
  });
};

export const generateQuote = () => apiCall(API_ENDPOINTS.GENERATE_QUOTE, { method: 'POST' });

export const downloadQuote = () => {
  return fetch(API_ENDPOINTS.DOWNLOAD_QUOTE, {
    method: 'GET',
    credentials: 'include'
  });
};

export const editProposalField = (data) => 
  apiCall(API_ENDPOINTS.EDIT_PROPOSAL_FIELD, {
    method: 'POST',
    body: JSON.stringify(data)
  });

export const formatNumber = (number) => 
  apiCall(API_ENDPOINTS.FORMAT_NUMBER, {
    method: 'POST',
    body: JSON.stringify({ number })
  });

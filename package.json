{"name": "pi-underwriting-portal", "version": "1.0.0", "description": "Professional Indemnity Underwriting Portal", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd server && python main.py", "install:all": "npm install && cd frontend && npm install", "build": "cd frontend && npm run build", "start": "npm run dev"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "jest": "^30.0.0", "concurrently": "^8.2.2"}, "keywords": ["insurance", "underwriting", "professional-indemnity"], "author": "", "license": "ISC"}
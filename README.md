# PI Underwriting Portal

A Professional Indemnity insurance underwriting portal that allows users to upload proposal documents (PDF/DOCX) and generate insurance quotes.

## Features

- **File Upload**: Support for PDF and DOCX proposal files
- **Document Processing**: Automatic text extraction from uploaded documents
- **Quote Generation**: AI-powered insurance quote generation
- **Excel Export**: Download quotes as Excel files
- **Real-time Processing**: Live updates during file processing

## Project Structure

```
├── frontend/          # React frontend application
├── server/           # Flask backend API
├── package.json      # Root package.json for development scripts
└── README.md         # This file
```

## Quick Start

### Prerequisites

- Node.js (v16 or higher)
- Python 3.8+
- pip (Python package manager)

### Installation

1. **Install all dependencies:**
   ```bash
   npm run install:all
   ```

2. **Set up Python environment:**
   ```bash
   cd server
   pip install -r requirements.txt
   ```

3. **Environment Variables:**
   Create a `.env` file in the `server` directory:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   ```

### Development

**Start both frontend and backend:**
```bash
npm run dev
```

This will start:
- Frontend on http://localhost:5173
- Backend on http://127.0.0.1:8080

**Start individually:**
```bash
# Frontend only
npm run dev:frontend

# Backend only
npm run dev:backend
```

## API Configuration

The project uses a simplified proxy configuration:

### Frontend (Vite)
- All API calls are prefixed with `/api`
- Vite proxy redirects `/api/*` to `http://127.0.0.1:8080/*`
- Centralized API configuration in `frontend/src/config/api.js`

### Backend (Flask)
- Simplified CORS configuration allowing all routes from frontend
- Single CORS rule instead of per-endpoint configuration
- Server runs on port 8080

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/upload/proposal` | POST | Upload proposal documents |
| `/upload/financials` | POST | Upload financial documents |
| `/generate_quote` | POST | Generate insurance quote |
| `/download_quote` | GET | Download quote as Excel |
| `/edit_proposal_field` | POST | Edit proposal field values |
| `/clear_proposal` | POST | Clear proposal data |

## File Upload Support

- **Supported formats**: PDF, DOCX
- **Maximum file size**: 50MB per file
- **Multiple files**: Yes
- **Drag & drop**: Supported
- **File validation**: Client and server-side validation

## Development Scripts

```bash
npm run dev          # Start both frontend and backend
npm run dev:frontend # Start frontend only
npm run dev:backend  # Start backend only
npm run build        # Build frontend for production
npm run install:all  # Install all dependencies
```

## Configuration Files

- `frontend/vite.config.js` - Vite configuration with proxy setup
- `frontend/src/config/api.js` - Centralized API configuration
- `server/main.py` - Flask application with simplified CORS
- `package.json` - Root package.json with development scripts

## Troubleshooting

### Common Issues

1. **CORS errors**: Ensure both frontend (5173) and backend (8080) are running
2. **File upload fails**: Check file format (PDF/DOCX) and size (<50MB)
3. **API calls fail**: Verify the backend server is running on port 8080

### Port Configuration

If you need to change ports:

1. **Frontend port**: Modify `server.port` in `frontend/vite.config.js`
2. **Backend port**: Change the port in `server/main.py` (last line)
3. **Update proxy**: Modify `SERVER_URL` in `frontend/vite.config.js`

## Contributing

1. Make changes to the appropriate directory (`frontend/` or `server/`)
2. Test using `npm run dev`
3. Ensure all API calls use the centralized configuration
4. Update this README if adding new features or endpoints

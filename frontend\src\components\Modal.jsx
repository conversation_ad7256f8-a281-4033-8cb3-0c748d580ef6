import React, { useEffect, useRef, useState } from "react";
import { useSummary } from "../context/SummaryContext";
import { uploadFiles } from "../config/api";
import Spinner from "./Spinner";

const Modal = ({ buttonText, uploadType }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [files, setFiles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const trigger = useRef(null);
  const modal = useRef(null);
  const { updateSummary } = useSummary();

  useEffect(() => {
    const clickHandler = (event) => {
      if (
        modal.current &&
        !modal.current.contains(event.target) &&
        trigger.current &&
        !trigger.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", clickHandler);
    return () => document.removeEventListener("mousedown", clickHandler);
  }, []);

  useEffect(() => {
    const keyHandler = (event) => {
      if (event.key === "Escape") {
        setIsOpen(false);
      }
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  }, []);

  const handleFileChange = (event) => {
    event.preventDefault();
    const newFiles = Array.from(event.target.files);
    setFiles([...files, ...newFiles]);
  };

  const removeFile = (index) => {
    setFiles(files.filter((_, i) => i !== index));
  };

  const handleUpload = async (e) => {
    e.preventDefault();
    if (files.length === 0) {
      alert("Please select files to upload");
      return;
    }

    setIsLoading(true);
    setIsOpen(false); // Close modal immediately

    try {
      const data = await uploadFiles(files, uploadType);

      console.log('Upload response:', data);
      updateSummary(
        uploadType.toLowerCase().startsWith('financ') ? 'financial' : 'proposal',
        data
      );
      setFiles([]);
    } catch (error) {
      console.error("Error uploading file:", error);
      alert(error.message || "An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative inline-flex items-center">
      <button
        ref={trigger}
        onClick={() => setIsOpen(true)}
        className="rounded-md border border-gray-300 bg-blue-500 px-6 py-3 text-white font-medium text-navy hover:bg-blue-700 transition-colors duration-200"
        disabled={isLoading}
      >
        {buttonText}
      </button>
      {isLoading && (
        <div className="ml-2">
          <Spinner />
        </div>
      )}

      {isOpen && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div className="absolute inset-0 backdrop-blur-sm bg-white/30"></div>
          <div
            ref={modal}
            className="relative w-3/5 rounded-lg bg-white px-8 py-12 text-center shadow-lg"
          >
            <h3 className="pb-4 text-xl font-semibold text-gray-800 sm:text-2xl">
              Upload {uploadType} Document
            </h3>
            <span className="mx-auto mb-6 block h-1 w-24 rounded bg-primary"></span>
            
            <form onSubmit={handleUpload} className="space-y-4">
              <div>
                <label className="block text-sm font-bold text-gray-600">
                  Attach Document
                </label>
                <div className="flex items-center justify-center w-full">
                  <label className="flex flex-col items-center justify-center w-full h-40 border-2 border-dashed border-gray-300 rounded-lg p-4 cursor-pointer hover:border-indigo-500">
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <svg className="w-10 h-10 mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                      </svg>
                      <p className="mb-2 text-sm text-gray-500">
                        <span className="font-semibold">Click to upload</span> or drag and drop
                      </p>
                      <p className="text-xs text-gray-500">PDF and DOCX files only</p>
                    </div>
                    <input
                      type="file"
                      className="hidden"
                      accept=".pdf,.docx"
                      multiple
                      onChange={handleFileChange}
                    />
                  </label>
                </div>

                <div className="mt-4 text-left">
                  {files.map((file, index) => (
                    <div key={index} className="flex justify-between items-center bg-gray-100 p-2 rounded-md mt-2">
                      <span className="text-sm text-gray-700">{file.name}</span>
                      <button
                        type="button"
                        className="text-red-500 text-sm"
                        onClick={() => removeFile(index)}
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-6 flex justify-center space-x-4">
                <button
                  type="button"
                  onClick={() => setIsOpen(false)}
                  className="rounded-md border border-red-600 bg-red-600 px-6 py-3 text-white font-medium transition hover:bg-red-700"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="rounded-md border border-green-600 bg-green-600 px-6 py-3 text-white font-medium transition hover:bg-green-700"
                >
                  Upload {uploadType}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

const FormBody = () => {
  return (
    <div className="flex justify-center items-center bg-gray-100 pt-6 pb-8">
      <div className="w-[60%] bg-white rounded-2xl shadow-lg p-4 border border-gray-300">
        <div className="flex gap-2">
          {/* Left section - Proposal Upload */}
          <div className="flex-1 border-r border-gray-300 pr-2">
            <h3 className="text-lg font-medium text-gray-700 mb-2">Upload Proposal Form</h3>
            <Modal buttonText="Upload" uploadType="Proposal" />
          </div>
          
          {/* Right section - Financial Upload */}
          <div className="flex-1 pl-2">
            <h3 className="text-lg font-medium text-gray-700 mb-2">Upload Audited Financial Statements</h3>
            <Modal buttonText="Upload" uploadType="Financial" />
          </div>
        </div>
      </div>
    </div>
  );
};

const App = () => {
  return <FormBody />;
};

export default App;

import React, { useState } from "react";
import { useSummary } from '../context/SummaryContext';
import { generateQuote, downloadQuote, editProposalField } from '../config/api';
import Alert from '@mui/material/Alert';
import AlertTitle from '@mui/material/AlertTitle';
import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import TextField from '@mui/material/TextField';
import CircularProgress from '@mui/material/CircularProgress';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import MenuItem from '@mui/material/MenuItem';

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    //backgroundColor: '#38aee4', //
    backgroundColor: '#407bad', //

    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(odd)': {
    backgroundColor: theme.palette.action.hover,
  },
  '&:last-child td, &:last-child th': {
    border: 0,
  },
}));

const FinancialSummaryTable = ({ data }) => {
  if (!data || !data.financial_summary || data.financial_summary.length === 0) {
    return <p className="text-gray-500 italic">No financial data available</p>;
  }

  // Compute status based on profit_after_tax
  const computeStatus = (profitAfterTax) => {
    const num = Number(String(profitAfterTax).replace(/,/g, ''));
    if (!isNaN(num)) {
      if (num > 0) return 'Profitable';
      if (num < 0) return 'Not profitable';
    }
    return '-';
  };

  // Sort by fiscal_year descending (most recent first)
  const sortedFinancialSummary = [...data.financial_summary].sort((a, b) => {
    // Try to parse as integer, fallback to string compare
    const yearA = parseInt(a.fiscal_year, 10);
    const yearB = parseInt(b.fiscal_year, 10);
    if (!isNaN(yearA) && !isNaN(yearB)) {
      return yearB - yearA;
    }
    return String(b.fiscal_year).localeCompare(String(a.fiscal_year));
  });

  // Check if the client is profitable (any year profitable)
  const isProfitable = sortedFinancialSummary.some(item => {
    const num = Number(String(item.profit_after_tax).replace(/,/g, ''));
    return !isNaN(num) && num > 0;
  });

  return (
    <div className="overflow-x-auto">
      <h3 className="text-l font-semibold mb-4 text-center">Financial Summary</h3>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 700 }} aria-label="financial summary table">
          <TableHead>
            <TableRow>
              <StyledTableCell>Fiscal Year</StyledTableCell>
              <StyledTableCell>Status</StyledTableCell>
              <StyledTableCell align="right">Revenue (KES) </StyledTableCell>
              <StyledTableCell align="right">Operating Profit (KES) </StyledTableCell>
              <StyledTableCell align="right">Profit Before Tax (KES) </StyledTableCell>
              <StyledTableCell align="right">Profit After Tax (KES) </StyledTableCell>
              <StyledTableCell>Source File</StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedFinancialSummary.map((item, index) => (
              <StyledTableRow key={index}>
                <StyledTableCell component="th" scope="row">{item.fiscal_year}</StyledTableCell>
                <StyledTableCell>{computeStatus(item.profit_after_tax)}</StyledTableCell>
                <StyledTableCell align="right">{item.revenue}</StyledTableCell>
                <StyledTableCell align="right">{item.operating_profit}</StyledTableCell>
                <StyledTableCell align="right">{item.profit_before_tax}</StyledTableCell>
                <StyledTableCell align="right">{item.profit_after_tax}</StyledTableCell>
                <StyledTableCell>{item.source_file || '-'}</StyledTableCell>
              </StyledTableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      <div className="mt-4">
        {isProfitable ? (
          <Alert 
            severity="success"
            sx={{
            //  backgroundColor: '#b2ecaa', // Light green background
              backgroundColor: '#a2da9a', // Light green background
              //backgroundColor: '#b2ecaa', // Light green background

              color: '#00632a', // Dark green text
              '& .MuiAlert-icon': {
                color: '#2e7d32', // Dark green icon
              },
              '& .MuiAlertTitle-root': {
                color: '#2e7d32', // Dark green title
                fontWeight: 'bold',
              },
            }}
          >
            <AlertTitle>Success</AlertTitle>
            The client is profitable.
          </Alert>
        ) : (
          <Alert severity="error">
            <AlertTitle>Error</AlertTitle>
            The client is not profitable.
          </Alert>
        )}
      </div>
    </div>
  );
};

const ProposalSummaryTable = ({ data }) => {
  if (!data || Object.keys(data).length === 0) {
    return <p className="text-gray-500 italic">No proposal data available</p>;
  }

  const firstProposal = Object.values(data)[0];
  const proposalData = firstProposal.proposal_summary || firstProposal;
  const headers = proposalData.headers || [];
  const values = proposalData.values || [];

  // Fields that should have KES and comma formatting
  const monetaryFields = [
    'Estimated Annual Income',
    'Limit of Indemnity (Cover Limit)',
    'Deductible/Excess Applicable'
  ];

  // Format number with commas and KES prefix
  const formatValue = (value, header) => {
    // Handle null, undefined, or empty values
    if (value === null || value === undefined || value === '') {
      return '-';
    }

    // Only format monetary fields with KES and commas
    if (monetaryFields.includes(header)) {
      // Check if the value is a number or a string that looks like a number
      if (typeof value === 'number' || !isNaN(Number(String(value).replace(/,/g, '')))) {
        const numValue = typeof value === 'number' ? value : Number(String(value).replace(/,/g, ''));
        return `KES ${numValue.toLocaleString('en-US', {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        })}`;
      }
    }
    return value;
  };

  return (
    <div className="overflow-x-auto mt-6">
      <h3 className="text-l font-semibold mb-3 text-center">Proposal Summary</h3>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 700 }} aria-label="proposal summary table">
          <TableHead>
            <TableRow>
              <StyledTableCell>Field</StyledTableCell>
              <StyledTableCell>Value</StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {headers.map((header, index) => (
              <StyledTableRow key={index}>
                <StyledTableCell component="th" scope="row">{header}</StyledTableCell>
                <StyledTableCell>{formatValue(values[index], header)}</StyledTableCell>
              </StyledTableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};

const InsuranceHistoryTable = ({ data }) => {
  if (!data || Object.keys(data).length === 0) {
    return <p className="text-gray-500 italic">No insurance history data available</p>;
  }

  const firstProposal = Object.values(data)[0];
  const insuranceData = firstProposal.insurance_history || {};
  const headers = insuranceData.headers || [];
  const values = insuranceData.values || [];

  return (
    <div className="overflow-x-auto mt-6">
      <h3 className="text-l font-semibold mb-3 text-center">Insurance History</h3>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 700 }} aria-label="insurance history table">
          <TableHead>
            <TableRow>
              <StyledTableCell>Field</StyledTableCell>
              <StyledTableCell>Value</StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {headers.map((header, index) => (
              <StyledTableRow key={index}>
                <StyledTableCell component="th" scope="row">{header}</StyledTableCell>
                <StyledTableCell>{values[index]}</StyledTableCell>
              </StyledTableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};

const ExtensionsTable = ({ data }) => {
  if (!data || Object.keys(data).length === 0) {
    return <p className="text-gray-500 italic">No extensions data available</p>;
  }

  const firstProposal = Object.values(data)[0];
  const extensionsData = firstProposal.extensions || {};
  const headers = extensionsData.headers || [];
  const values = extensionsData.values || [];

  return (
    <div className="overflow-x-auto mt-6">
      <h3 className="text-l font-semibold mb-3 text-center">Extensions</h3>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 700 }} aria-label="extensions table">
          <TableHead>
            <TableRow>
              <StyledTableCell>Extension</StyledTableCell>
              <StyledTableCell>Status</StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {headers.map((header, index) => (
              <StyledTableRow key={index}>
                <StyledTableCell component="th" scope="row">{header}</StyledTableCell>
                <StyledTableCell>{values[index]}</StyledTableCell>
              </StyledTableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};

const QuotationTable = ({ data }) => {
  if (!data || !data.variables) return null;

  console.log('QuotationTable data:', data);
  console.log('Display values:', data.display_values);

  // Define the order of items and their display names
  const componentLabels = {
    'part_a': 'Annual fees',
    'part_b': 'Limit of Indemnity',
    'part_c': 'Profession Factor',
    'part_d': 'A + B + C',
    'part_e': 'Loss of Documents extension',
    'part_f': 'Libel & Slander extension',
    'part_g': 'Dishonesty of Employees extension',
    'part_h': 'Incoming/ outgoing partners',
    'part_i': 'Errors and ommissions',
    'part_j': 'Breach of Authority',
    'part_k': 'Basic Premium  ',
    'levies': 'Levies',
    'stamp_duty': 'Stamp Duty',
    'total_premium': 'Total Premium'
  };

  // Define the order of items
  const orderedKeys = [
    'part_a',
    'part_b',
    'part_c',
    'part_d',
    'part_e',
    'part_f',
    'part_g',
    'part_h',
    'part_i',
    'part_j',
    'part_k',
    'levies',
    'stamp_duty',
    'total_premium'
  ];

  // Format number with commas
  const formatNumber = (num) => {
    if (typeof num === 'number') {
      return num.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      });
    }
    return num;
  };

  // Create ordered entries using display_values if available
  const orderedEntries = orderedKeys
    .filter(key => key in data.variables)
    .map(key => {
      let value;
      if (data.display_values && data.display_values[key]) {
        // Use the pre-formatted value from the backend
        value = `KES ${data.display_values[key]}`;
      } else {
        // Fallback to formatting the raw value
        value = `KES ${formatNumber(data.variables[key])}`;
      }
      console.log(`Mapping ${key}:`, { value, display_value: data.display_values?.[key], raw_value: data.variables[key] });
      return [key, value];
    });

  console.log('Ordered entries:', orderedEntries);

  return (
    <div className="overflow-x-auto mt-6">
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 700 }} aria-label="quotation calculations table">
          <TableHead>
            <TableRow>
              <StyledTableCell>Component</StyledTableCell>
              <StyledTableCell align="right">Amount</StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {orderedEntries.map(([key, value], index) => {
              const isTotal = key === 'total_premium';
              return (
                <StyledTableRow 
                  key={index}
                  sx={{
                    backgroundColor: isTotal ? 'rgba(25, 118, 210, 0.08)' : 'inherit'
                  }}
                >
                  <StyledTableCell component="th" scope="row">
                    {componentLabels[key]}
                  </StyledTableCell>
                  <StyledTableCell align="right">
                    {value}
                  </StyledTableCell>
                </StyledTableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};

// Helper to get all editable fields from the summary
function flattenProposalSummary(proposalSummary) {
  const fields = [];
  ["proposal_summary", "extensions", "insurance_history"].forEach(section => {
    if (proposalSummary[section]) {
      proposalSummary[section].headers.forEach((header, idx) => {
        fields.push({
          section,
          header,
          value: proposalSummary[section].values[idx]
        });
      });
    }
  });
  return fields;
}

const EditableProposalSummary = ({ proposalSummary, filename, onQuotationResult, setEditMode, onProposalSummaryUpdate }) => {
  // Flatten fields for easier editing
  const [fields, setFields] = useState(flattenProposalSummary(proposalSummary));
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [validation, setValidation] = useState({});
  const [showRequiredError, setShowRequiredError] = useState(false);

  // Get the first 11 headers from proposalSummary for required fields
  const requiredHeaders = proposalSummary?.headers?.slice(0, 11) || [];

  // Field type mapping for validation and input type
  const fieldTypeMap = {
    // Proposal Summary
    'Name of Cedant': 'text',
    'Name of Broker': 'text',
    'Period of Cover ': 'text',
    'Number of Business Partners': 'number',
    'Number of Qualified staff': 'number',
    'Number of Unqualified Staff Employed': 'number',
    'Other staff': 'number',
    'Estimated Annual Income': 'number',
    'Limit of Indemnity (Cover Limit)': 'number',
    'Deductible/Excess Applicable': 'number',
    'Occupation of the Insured': 'text',
    // Extensions (all boolean)
    'Defamation Coverage': 'boolean',
    'Loss of Documents': 'boolean',
    'Errors and ommissions': 'boolean',
    'Incoming/Outgoing partners': 'boolean',
    'Dishonesty of employees': 'boolean',
    'Breach of Authority': 'boolean',
    // Insurance History (all boolean except Insurance Company)
    'Insurance Company': 'text',
    'Declined previous insurance proposal': 'boolean',
    'Terminated/ refused insurance renewal': 'boolean',
    'Claims setted/ outstanding': 'boolean',
    'Claim circumstance awareness': 'boolean',
  };

  // Helper to determine field type
  const getFieldType = (header, section) => {
    const normalizedHeader = header.trim().toLowerCase();
    if (normalizedHeader === 'insurance company' || normalizedHeader === 'insurance company') return 'text';
    if (fieldTypeMap[header]) return fieldTypeMap[header];
    if (section === 'extensions' || (section === 'insurance_history' && normalizedHeader !== 'insurance company' && normalizedHeader !== 'insurance company')) return 'boolean';
    return 'text';
  };

  // Helper to determine if a field is required (none are required now)
  const requiredProposalFields = [
    'Name of Cedant',
    'Name of Broker',
    'Period of Cover',
    'Number of Business Partners',
    'Number of Qualified staff',
    'Number of Unqualified staff',
    'Other staff',
    'Estimated Annual Income',
    'Limit of Indemnity (Cover Limit)',
    'Occupation of the Insured',
    'Deductible/Excess Applicable',
  ];
  const isRequiredField = (header, section) => {
    return section === 'proposal_summary' && requiredProposalFields.includes(header);
  };

  // Group fields by section for section headers
  const groupedFields = fields.reduce((acc, field) => {
    if (!acc[field.section]) acc[field.section] = [];
    acc[field.section].push(field);
    return acc;
  }, {});

  // Validation logic
  const validateField = (field) => {
    const type = getFieldType(field.header, field.section);
    const value = field.value;
    // Required field check
    if (isRequiredField(field.header, field.section) && (value === '' || value === null || value === undefined)) {
      return 'Required';
    }
    // Special validation for Insurance Company in insurance_history
    if (field.section === 'insurance_history' && field.header === 'Insurance Company') {
      if (value === '' || value === null || value === undefined) {
        return '';
      }
      if (typeof value !== 'string' || !isNaN(Number(value))) {
        return 'Must be text';
      }
      return '';
    }
    if (value === '' || value === null || value === undefined) {
      return '';
    }
    if (type === 'number') {
      if (isNaN(Number(value))) {
        return 'Must be a number';
      }
      if (Number(value) < 0) {
        return 'Must be non-negative';
      }
    }
    if (type === 'boolean') {
      if (value !== 'Yes' && value !== 'No') {
        return 'Select Yes or No';
      }
    }
    return '';
  };

  // Validate all fields
  const validateAll = () => {
    const newValidation = {};
    let hasError = false;
    fields.forEach((field, idx) => {
      const errorMsg = validateField(field);
      if (errorMsg) hasError = true;
      newValidation[`${field.section}__${field.header}`] = errorMsg;
    });
    setValidation(newValidation);
    return !hasError;
  };

  // Handle input change
  const handleChange = (section, header, newValue) => {
    setFields(prev =>
      prev.map((field) =>
        field.section === section && field.header === header
          ? { ...field, value: newValue }
          : field
      )
    );
    // Validate on change
    setValidation(prev => ({
      ...prev,
      [`${section}__${header}`]: validateField({ section, header, value: newValue })
    }));
    setShowRequiredError(false);
  };

  // Update backend and generate quote
  const handleGenerateQuotation = async () => {
    if (!validateAll()) {
      setShowRequiredError(true);
      return;
    }
    setShowRequiredError(false);
    setLoading(true);
    setSuccess(false);
    setError(null);
    try {
      // Update each field in backend
      for (const field of fields) {
        await editProposalField({
          filename,
          section: field.section,
          field: field.header,
          value: field.value
        });
      }
      // Generate new quotation
      const res = await generateQuote();
      onQuotationResult(res.data);
      setSuccess(true);
      // Build updated proposalSummary object
      const updatedProposal = { ...proposalSummary };
      ["proposal_summary", "extensions", "insurance_history"].forEach(section => {
        if (updatedProposal[section]) {
          updatedProposal[section] = {
            ...updatedProposal[section],
            values: updatedProposal[section].headers.map((header, idx) => {
              const field = fields.find(f => f.section === section && f.header === header);
              return field ? field.value : updatedProposal[section].values[idx];
            })
          };
        }
      });
      if (onProposalSummaryUpdate) {
        onProposalSummaryUpdate(updatedProposal);
      }
      if (setEditMode) {
        setEditMode(false);
      }
    } catch (err) {
      setError(
        err.response?.data?.error ||
        err.message ||
        "An error occurred while updating or generating the quotation."
      );
    }
    setLoading(false);
  };

  // Section display names
  const sectionNames = {
    proposal_summary: 'Proposal Summary',
    extensions: 'Extensions',
    insurance_history: 'Insurance History',
  };

  return (
    <div style={{ padding: 24, marginBottom: 24, maxWidth: '100%', overflowX: 'auto' }}>
      <h2 className="text-lg font-semibold mb-4 text-gray-800">Edit Proposal Form</h2>
      <TableContainer sx={{ maxHeight: 'unset', height: 'auto', boxShadow: 'none', border: 'none', background: 'none' }}>
        <Table stickyHeader aria-label="editable proposal summary table">
          <TableHead>
            <TableRow>
              <StyledTableCell>Field</StyledTableCell>
              <StyledTableCell>Value</StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {Object.entries(groupedFields).map(([section, sectionFields], sIdx) => [
              <StyledTableRow key={section}>
                <StyledTableCell colSpan={2} sx={{ backgroundColor: '#e3eafc', fontWeight: 'bold' }}>
                  {sectionNames[section] || section}
                </StyledTableCell>
              </StyledTableRow>,
              ...sectionFields.map((field, idx) => {
                const type = getFieldType(field.header, field.section);
                const key = `${field.section}__${field.header}`;
                // --- Fix: Always render Insurance Company as text input ---
                const normalizedHeader = field.header.trim().toLowerCase();
                const isInsuranceCompany = normalizedHeader === 'insurance company' || normalizedHeader === 'insurance company';
                return (
                  <StyledTableRow key={key}
                    sx={{ backgroundColor: idx % 2 === 0 ? 'inherit' : '#f5f7fa' }}
                  >
                    <StyledTableCell>
                      {field.header}
                    </StyledTableCell>
                    <StyledTableCell>
                      {type === 'boolean' && !isInsuranceCompany ? (
                        <TextField
                          select
                          variant="outlined"
                          size="small"
                          value={field.value ?? ''}
                          onChange={e => handleChange(field.section, field.header, e.target.value)}
                          fullWidth
                          error={!!validation[key]}
                          helperText={validation[key]}
                        >
                          <MenuItem value="Yes">Yes</MenuItem>
                          <MenuItem value="No">No</MenuItem>
                        </TextField>
                      ) : (
                        <TextField
                          variant="outlined"
                          size="small"
                          type={type === 'number' ? 'number' : 'text'}
                          value={field.value ?? ''}
                          onChange={e => handleChange(field.section, field.header, e.target.value)}
                          fullWidth
                          error={!!validation[key]}
                          helperText={validation[key]}
                          InputProps={{
                            inputProps: type === 'number' ? { min: 0 } : {},
                            style: { background: '#fff' }
                          }}
                        />
                      )}
                    </StyledTableCell>
                  </StyledTableRow>
                );
              })
            ])}
          </TableBody>
        </Table>
      </TableContainer>
      <div className="flex items-center mt-6 gap-4">
        <button
          onClick={handleGenerateQuotation}
          disabled={loading}
          className={`px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-green-400 disabled:cursor-not-allowed flex items-center`}
        >
          {loading ? (
            <CircularProgress size={22} sx={{ color: 'white', mr: 1 }} />
          ) : (
            <CheckCircleIcon sx={{ mr: 1 }} />
          )}
          Save & Generate Quotation
        </button>
        {showRequiredError && !loading && (
          <span className="text-red-700 flex items-center"><ErrorIcon sx={{ mr: 0.5 }} /> fill all required fields</span>
        )}
        {success && (
          <span className="text-green-700 flex items-center"><CheckCircleIcon sx={{ mr: 0.5 }} /> Saved!</span>
        )}
        {error && (
          <span className="text-red-700 flex items-center"><ErrorIcon sx={{ mr: 0.5 }} /> {error}</span>
        )}
      </div>
    </div>
  );
};

const Summary = () => {
  const { financialSummary, proposalSummary, setProposalSummary } = useSummary();
  const [quotation, setQuotation] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);

  // Get the filename and proposal for editing
  const filename = proposalSummary && Object.keys(proposalSummary).length > 0 ? Object.keys(proposalSummary)[0] : null;
  const proposalForEdit = filename ? proposalSummary[filename] : null;

  // Function to check if any of the last 4 insurance history values are "Yes"
  const hasInsuranceWarning = () => {
    if (!proposalSummary || Object.keys(proposalSummary).length === 0) return false;
    
    const firstProposal = Object.values(proposalSummary)[0];
    const insuranceData = firstProposal.insurance_history || {};
    const values = insuranceData.values || [];
    
    // Get the last 4 values and check if any are "Yes"
    const lastFourValues = values.slice(-4);
    return lastFourValues.some(value => value?.toString().toLowerCase() === 'yes');
  };

  const isEmpty = (!proposalSummary || Object.keys(proposalSummary).length === 0) && (!financialSummary || !financialSummary.financial_summary || financialSummary.financial_summary.length === 0);

  const handleGenerateQuote = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await generateQuote();
      setQuotation(data);
      console.log('Generated quotation:', data);
    } catch (err) {
      setError(err.message);
      console.error('Error generating quotation:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownloadExcel = async () => {
    try {
      const response = await downloadQuote();

      // Check if the response is empty
      const contentLength = response.headers.get('Content-Length');
      if (contentLength === '0') {
        throw new Error('Empty response received from server');
      }

      const blob = await response.blob();
      if (blob.size === 0) {
        throw new Error('Empty file received from server');
      }

      // Get filename from Content-Disposition header
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = 'quotation.xlsx'; // Default filename
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename\*=UTF-8''([^;]+)/);
        if (filenameMatch) {
          filename = decodeURIComponent(filenameMatch[1]);
        }
      }

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Error downloading excel:', err);
      setError(err.message);
    }
  };

  return (
    <div className={`w-4/5 mx-auto bg-white rounded-lg shadow-lg p-6 pt-6 border border-gray-300 mb-4 ${isEmpty ? 'min-h-[50px]' : ''}`}>
      <h2 className="text-lg font-semibold text-left text-gray-800 mb-4">
        Extracted Summary
      </h2>
      <div
        className="p-6 border border-gray-200 rounded-md mb-4"
        style={isEmpty ? { minHeight: '50px', display: 'flex', alignItems: 'center', justifyContent: 'center' } : {}}
      >
        {isEmpty ? (
          <div className="text-center text-gray-500 italic w-full">Upload .pdf files to generate summaries</div>
        ) : (
          <>
            <FinancialSummaryTable data={financialSummary} />
            {editMode && proposalForEdit && filename ? (
              <EditableProposalSummary
                proposalSummary={proposalForEdit}
                filename={filename}
                onQuotationResult={setQuotation}
                setEditMode={setEditMode}
                onProposalSummaryUpdate={updatedProposal => {
                  // Update the proposalSummary in context
                  setProposalSummary(prev => ({
                    ...prev,
                    [filename]: updatedProposal
                  }));
                }}
              />
            ) : (
              <>
                <ProposalSummaryTable data={proposalSummary} />
                {(() => {
                  if (!proposalSummary || Object.keys(proposalSummary).length === 0) return null;
                  const firstProposal = Object.values(proposalSummary)[0];
                  const proposalData = firstProposal.proposal_summary || firstProposal;
                  const headers = proposalData.headers || [];
                  const values = proposalData.values || [];
                  const first11 = values.slice(0, 11);
                  const missingCount = first11.filter(
                    v => v === null || v === undefined || v === '' || v === '-'
                  ).length;
                  if (missingCount >= 5) {
                    return (
                      <Alert severity="error" sx={{ mt: 2 }}>
                        <AlertTitle>Error</AlertTitle>
                        Poor proposal form quality.
                      </Alert>
                    );
                  }
                  return null;
                })()}
                <ExtensionsTable data={proposalSummary} />
                <InsuranceHistoryTable data={proposalSummary} />
              </>
            )}
            {(() => {
              if (!proposalSummary || Object.keys(proposalSummary).length === 0) return null;
              const firstProposal = Object.values(proposalSummary)[0];
              const insuranceData = firstProposal.insurance_history || {};
              const headers = insuranceData.headers || [];
              const values = insuranceData.values || [];
              // Normalize function
              const normalize = str => str?.toString().trim().toLowerCase();
              // Map normalized header to value
              const normalizedFieldMap = {};
              headers.forEach((header, idx) => {
                normalizedFieldMap[normalize(header)] = values[idx];
              });
              const alerts = [];
              if (
                normalizedFieldMap[normalize('Declined previous insurance proposal')]?.toString().toLowerCase() === 'yes' ||
                normalizedFieldMap[normalize('Declined previous insurance proposal')]?.toString().toLowerCase() === 'yes'
              ) {
                alerts.push(
                  <Alert severity="info" sx={{ mt: 2 }} key="declined">
                    <AlertTitle>Info</AlertTitle>
                    The cedant has previously had a declined insurance proposal.
                    Request for more information.
                  </Alert>
                );
              }
              if (
                normalizedFieldMap[normalize('Terminated/ refused insurance renewal')]?.toString().toLowerCase() === 'yes' ||
                normalizedFieldMap[normalize('Terminated/ refused insurance renewal')]?.toString().toLowerCase() === 'yes'
              ) {
                alerts.push(
                  <Alert severity="info" sx={{ mt: 2 }} key="terminated">
                    <AlertTitle>Info</AlertTitle>
                    The cedant has previously had insurance renewal terminated or refused.
                    Request for more information.
                  </Alert>
                );
              }
              if (normalizedFieldMap[normalize('Claims setted/ outstanding')]?.toString().toLowerCase() === 'yes') {
                alerts.push(
                  <Alert severity="info" sx={{ mt: 2 }} key="claims">
                    <AlertTitle>Info</AlertTitle>
                    The cedant has previously had claims settled or outstanding.
                    Request for more information.
                  </Alert>
                );
              }
              if (normalizedFieldMap[normalize('Claim circumstance awareness')]?.toString().toLowerCase() === 'yes') {
                alerts.push(
                  <Alert severity="info" sx={{ mt: 2 }} key="circumstance">
                    <AlertTitle>Info</AlertTitle>
                    The cedant has claim circumstance awareness.
                    Request for more information.
                  </Alert>
                );
              }
              return alerts.length > 0 ? alerts : null;
            })()}
          </>
        )}
      </div>
      <div className="flex justify-end mb-2">
        <button
          onClick={() => setEditMode((prev) => !prev)}
          className={`px-4 py-2 rounded-md ${editMode ? 'bg-gray-500 hover:bg-gray-600' : 'bg-yellow-500 hover:bg-yellow-600'} text-white`}
        >
          {editMode ? 'View Proposal Summary' : 'Edit Proposal Summary'}
        </button>
      </div>
      <h2 className="text-lg font-semibold text-left text-gray-800 mb-4">
        Quotation Breakdown
      </h2>
      <div className="p-6 border border-gray-200 rounded-md mb-4">
        {error && (
          <div className="text-red-600 mb-4">
            Error: {error}
          </div>
        )}
        
        {quotation && <QuotationTable data={quotation} />}
      </div>

      <div className="flex justify-between items-center mt-4">
        <button
          onClick={handleGenerateQuote}
          disabled={isLoading}
          className={`px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-green-400 disabled:cursor-not-allowed flex items-center`}
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Generating...
            </>
          ) : (
            'Generate quotation'
          )}
        </button>

        <button
          onClick={handleDownloadExcel}
          disabled={!quotation}
          className={`px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed flex items-center`}
        >
          <svg 
            className="w-5 h-5 mr-2" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth="2" 
              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
            />
          </svg>
          Download Excel Quotation
        </button>
      </div>
    </div>
  );
};

export default Summary;
